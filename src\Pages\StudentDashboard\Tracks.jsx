import React, { useState, useRef, useEffect, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Canvas, useFrame, useThree } from "@react-three/fiber";
import {
  OrbitControls,
  Text,
  Sphere,
  Line,
  Stars,
  Float,
  Trail,
} from "@react-three/drei";
import * as THREE from "three";
import {
  FiCode, // Web Development
  FiSmartphone, // Mobile App Development
  FiDatabase, // Data Science
  FiShield, // Cybersecurity
  FiCloud, // Cloud Computing
  FiSettings, // DevOps
  FiCpu, // Embedded & IoT
  FiLayers, // Blockchain
  FiCheckSquare, // QA & Testing
  FiPenTool, // UI/UX Design
  FiArrowLeft,
} from "react-icons/fi";

import { GiBrain, GiGamepad } from "react-icons/gi";

// Cosmic Career Tracks with 3D Positioning and Neon Colors
const careerTracks = [
  {
    id: "web-dev",
    name: "Web Development",
    icon: FiCode,
    color: "#00FFFF", // Cyan
    glowColor: "#00FFFF",
    position: [0, 0, 0],
    roadmap: [
      {
        id: 1,
        title: "HTML Basics",
        position: [0, 0, 0],
        level: 1,
        connections: [2],
      },
      {
        id: 2,
        title: "CSS Styling",
        position: [4, 2, -2],
        level: 1,
        connections: [3],
      },
      {
        id: 3,
        title: "JavaScript",
        position: [2, 5, 1],
        level: 2,
        connections: [4, 5],
      },
      {
        id: 4,
        title: "React/Vue",
        position: [6, 8, -1],
        level: 3,
        connections: [6],
      },
      {
        id: 5,
        title: "Backend APIs",
        position: [-2, 8, 3],
        level: 3,
        connections: [6],
      },
      {
        id: 6,
        title: "Full Stack",
        position: [2, 12, 0],
        level: 4,
        connections: [],
      },
    ],
  },
  {
    id: "mobile-dev",
    name: "Mobile Development",
    icon: FiSmartphone,
    color: "#FF00FF", // Magenta
    glowColor: "#FF00FF",
    position: [8, 0, 0],
    roadmap: [
      {
        id: 1,
        title: "Programming Basics",
        position: [0, 0, 0],
        level: 1,
        connections: [2],
      },
      {
        id: 2,
        title: "UI/UX Principles",
        position: [4, 3, 2],
        level: 1,
        connections: [3],
      },
      {
        id: 3,
        title: "React Native",
        position: [1, 6, -1],
        level: 2,
        connections: [4, 5],
      },
      {
        id: 4,
        title: "Native Development",
        position: [5, 9, 2],
        level: 3,
        connections: [6],
      },
      {
        id: 5,
        title: "App Store Deploy",
        position: [-3, 9, -2],
        level: 3,
        connections: [6],
      },
      {
        id: 6,
        title: "Advanced Features",
        position: [1, 13, 0],
        level: 4,
        connections: [],
      },
    ],
  },
  {
    id: "data-science",
    name: "Data Science",
    icon: FiDatabase,
    color: "#00FF00", // Green
    glowColor: "#00FF00",
    position: [16, 0, 0],
    roadmap: [
      {
        id: 1,
        title: "Statistics",
        position: [0, 0, 0],
        level: 1,
        connections: [2],
      },
      {
        id: 2,
        title: "Python/R",
        position: [3, 3, -1],
        level: 1,
        connections: [3],
      },
      {
        id: 3,
        title: "Data Analysis",
        position: [1, 6, 2],
        level: 2,
        connections: [4, 5],
      },
      {
        id: 4,
        title: "Machine Learning",
        position: [5, 9, -2],
        level: 3,
        connections: [6],
      },
      {
        id: 5,
        title: "Deep Learning",
        position: [-3, 9, 1],
        level: 3,
        connections: [6],
      },
      {
        id: 6,
        title: "Data Engineering",
        position: [1, 13, 0],
        level: 4,
        connections: [],
      },
    ],
  },
  {
    id: "cybersecurity",
    name: "Cybersecurity",
    icon: FiShield,
    color: "#FF4500", // Orange Red
    glowColor: "#FF4500",
    position: [24, 0, 0],
    roadmap: [
      {
        id: 1,
        title: "Network Basics",
        position: [0, 0, 0],
        level: 1,
        connections: [2],
      },
      {
        id: 2,
        title: "Security Principles",
        position: [4, 2, 1],
        level: 1,
        connections: [3],
      },
      {
        id: 3,
        title: "Ethical Hacking",
        position: [2, 5, -2],
        level: 2,
        connections: [4, 5],
      },
      {
        id: 4,
        title: "Penetration Testing",
        position: [6, 8, 1],
        level: 3,
        connections: [6],
      },
      {
        id: 5,
        title: "Incident Response",
        position: [-2, 8, -1],
        level: 3,
        connections: [6],
      },
      {
        id: 6,
        title: "Security Architecture",
        position: [2, 12, 0],
        level: 4,
        connections: [],
      },
    ],
  },
  {
    id: "ai-ml",
    name: "AI & Machine Learning",
    icon: GiBrain,
    color: "#9400D3", // Violet
    glowColor: "#9400D3",
    position: [32, 0, 0],
    roadmap: [
      {
        id: 1,
        title: "Math Foundations",
        position: [0, 0, 0],
        level: 1,
        connections: [2],
      },
      {
        id: 2,
        title: "Programming",
        position: [4, 3, 1],
        level: 1,
        connections: [3],
      },
      {
        id: 3,
        title: "ML Algorithms",
        position: [2, 6, -1],
        level: 2,
        connections: [4, 5],
      },
      {
        id: 4,
        title: "Deep Learning",
        position: [6, 9, 2],
        level: 3,
        connections: [6],
      },
      {
        id: 5,
        title: "Neural Networks",
        position: [-2, 9, -2],
        level: 3,
        connections: [6],
      },
      {
        id: 6,
        title: "AI Research",
        position: [2, 13, 0],
        level: 4,
        connections: [],
      },
    ],
  },
  {
    id: "game-dev",
    name: "Game Development",
    icon: GiGamepad,
    color: "#FFD700", // Gold
    glowColor: "#FFD700",
    position: [40, 0, 0],
    roadmap: [
      {
        id: 1,
        title: "Game Design",
        position: [0, 0, 0],
        level: 1,
        connections: [2],
      },
      {
        id: 2,
        title: "Programming",
        position: [3, 3, -1],
        level: 1,
        connections: [3],
      },
      {
        id: 3,
        title: "Game Engines",
        position: [1, 6, 1],
        level: 2,
        connections: [4, 5],
      },
      {
        id: 4,
        title: "3D Graphics",
        position: [5, 9, -1],
        level: 3,
        connections: [6],
      },
      {
        id: 5,
        title: "Physics & AI",
        position: [-3, 9, 2],
        level: 3,
        connections: [6],
      },
      {
        id: 6,
        title: "Game Publishing",
        position: [1, 13, 0],
        level: 4,
        connections: [],
      },
    ],
  },
];

// Cosmic Particle System
const CosmicParticles = () => {
  const particlesRef = useRef();
  const particleCount = 1000;

  const particles = useMemo(() => {
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      // Random positions in a large sphere
      const radius = Math.random() * 100 + 50;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      positions[i * 3 + 2] = radius * Math.cos(phi);

      // Random neon colors
      const colorChoice = Math.random();
      if (colorChoice < 0.2) {
        colors[i * 3] = 0;
        colors[i * 3 + 1] = 1;
        colors[i * 3 + 2] = 1; // Cyan
      } else if (colorChoice < 0.4) {
        colors[i * 3] = 1;
        colors[i * 3 + 1] = 0;
        colors[i * 3 + 2] = 1; // Magenta
      } else if (colorChoice < 0.6) {
        colors[i * 3] = 0;
        colors[i * 3 + 1] = 1;
        colors[i * 3 + 2] = 0; // Green
      } else if (colorChoice < 0.8) {
        colors[i * 3] = 1;
        colors[i * 3 + 1] = 0.5;
        colors[i * 3 + 2] = 0; // Orange
      } else {
        colors[i * 3] = 1;
        colors[i * 3 + 1] = 1;
        colors[i * 3 + 2] = 0; // Yellow
      }
    }

    return { positions, colors };
  }, []);

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y += 0.0005;
      particlesRef.current.rotation.x += 0.0002;
    }
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={particles.positions}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          count={particleCount}
          array={particles.colors}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.1}
        vertexColors
        transparent
        opacity={0.8}
        sizeAttenuation={true}
      />
    </points>
  );
};

// Cosmic Node Component with Glow Effects
const CosmicNode = ({ node, isSelected, onClick, trackColor, glowColor }) => {
  const meshRef = useRef();
  const glowRef = useRef();
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current) {
      // Gentle rotation
      meshRef.current.rotation.y += 0.005;

      // Pulsing effect
      const pulse = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.1;
      const scale = isSelected ? 1.5 * pulse : hovered ? 1.2 * pulse : pulse;
      meshRef.current.scale.setScalar(scale);
    }

    if (glowRef.current) {
      // Glow pulsing
      const glowPulse = 0.5 + Math.sin(state.clock.elapsedTime * 3) * 0.3;
      glowRef.current.scale.setScalar(isSelected ? 3 : hovered ? 2.5 : 2);
      glowRef.current.material.opacity = isSelected
        ? 0.4
        : hovered
        ? 0.3
        : glowPulse * 0.2;
    }
  });

  return (
    <group position={node.position}>
      {/* Outer Glow */}
      <Sphere ref={glowRef} args={[0.8, 16, 16]}>
        <meshBasicMaterial color={glowColor} transparent opacity={0.2} />
      </Sphere>

      {/* Main Node */}
      <Sphere
        ref={meshRef}
        args={[0.4, 32, 32]}
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <meshStandardMaterial
          color={trackColor}
          emissive={glowColor}
          emissiveIntensity={isSelected ? 0.5 : hovered ? 0.3 : 0.1}
          metalness={0.8}
          roughness={0.2}
        />
      </Sphere>

      {/* Node Label */}
      <Text
        position={[0, -1.2, 0]}
        fontSize={0.3}
        color={glowColor}
        anchorX="center"
        anchorY="middle"
        maxWidth={3}
        font="/fonts/orbitron.woff"
      >
        {node.title}
      </Text>

      {/* Selection Ring */}
      {(isSelected || hovered) && (
        <mesh rotation={[Math.PI / 2, 0, 0]}>
          <ringGeometry args={[0.6, 0.8, 32]} />
          <meshBasicMaterial
            color={glowColor}
            transparent
            opacity={0.6}
            side={THREE.DoubleSide}
          />
        </mesh>
      )}
    </group>
  );
};

// Cosmic Energy Trails Component
const CosmicEnergyTrails = ({ roadmap, trackColor, glowColor }) => {
  const trailRefs = useRef([]);

  useFrame((state) => {
    trailRefs.current.forEach((ref, index) => {
      if (ref) {
        // Animate energy flow along the trails
        const time = state.clock.elapsedTime;
        const offset = (time * 2 + index * 0.5) % (Math.PI * 2);
        ref.material.opacity = 0.3 + Math.sin(offset) * 0.2;
      }
    });
  });

  const connections = [];

  // Create connections based on the connections array in each node
  roadmap.forEach((node) => {
    node.connections?.forEach((targetId) => {
      const targetNode = roadmap.find((n) => n.id === targetId);
      if (targetNode) {
        const key = `trail-${node.id}-${targetId}`;
        connections.push(
          <group key={key}>
            {/* Main energy trail */}
            <Line
              ref={(ref) => trailRefs.current.push(ref)}
              points={[node.position, targetNode.position]}
              color={glowColor}
              lineWidth={3}
              transparent
              opacity={0.4}
            />
            {/* Secondary glow trail */}
            <Line
              points={[node.position, targetNode.position]}
              color={trackColor}
              lineWidth={1}
              transparent
              opacity={0.8}
            />
          </group>
        );
      }
    });
  });

  return <>{connections}</>;
};

// Cosmic Camera Controller
const CosmicCameraController = () => {
  const { camera } = useThree();

  useFrame((state) => {
    // Gentle camera movement for immersion
    camera.position.x += Math.sin(state.clock.elapsedTime * 0.1) * 0.01;
    camera.position.y += Math.cos(state.clock.elapsedTime * 0.15) * 0.005;
  });

  return null;
};

// Cosmic 3D Roadmap Scene
const CosmicRoadmapScene = ({ track, onBack }) => {
  const [selectedNode, setSelectedNode] = useState(null);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className="fixed inset-0 bg-gradient-to-br from-black via-purple-900/20 to-black z-50"
      style={{
        background: `
          radial-gradient(ellipse at top, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(ellipse at bottom, rgba(255, 0, 128, 0.2) 0%, transparent 50%),
          linear-gradient(180deg, #000000 0%, #0a0a0a 50%, #000000 100%)
        `,
      }}
    >
      {/* Cosmic Header */}
      <div className="absolute top-0 left-0 right-0 z-10 p-6 bg-black/60 backdrop-blur-md border-b border-cyan-500/30">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-4">
            <motion.button
              whileHover={{
                scale: 1.05,
                boxShadow: `0 0 20px ${track.glowColor}`,
              }}
              whileTap={{ scale: 0.95 }}
              onClick={onBack}
              className="flex items-center space-x-2 px-6 py-3 bg-gray-900/80 hover:bg-gray-800/80 rounded-lg transition-all duration-300 border border-cyan-500/30"
              style={{
                boxShadow: `0 0 10px ${track.glowColor}40`,
                color: track.glowColor,
              }}
            >
              <FiArrowLeft className="w-5 h-5" />
              <span className="font-medium">Back to Galaxy</span>
            </motion.button>
            <div className="flex items-center space-x-4">
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center"
                style={{
                  backgroundColor: `${track.glowColor}20`,
                  boxShadow: `0 0 20px ${track.glowColor}60`,
                }}
              >
                <track.icon
                  className="w-6 h-6"
                  style={{ color: track.glowColor }}
                />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white mb-1 font-mono">
                  {track.name}
                </h1>
                <p className="text-cyan-300 text-sm">Neural Pathway Map</p>
              </div>
            </div>
          </div>
          {selectedNode && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-black/80 px-6 py-4 rounded-lg border"
              style={{
                borderColor: track.glowColor,
                boxShadow: `0 0 20px ${track.glowColor}40`,
              }}
            >
              <p className="text-cyan-300 text-sm mb-1">Active Node:</p>
              <p className="font-bold text-white text-lg">
                {selectedNode.title}
              </p>
              <div className="w-full h-1 bg-gray-800 rounded-full mt-2 overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 1 }}
                  className="h-full rounded-full"
                  style={{ backgroundColor: track.glowColor }}
                />
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Cosmic 3D Canvas */}
      <Canvas
        camera={{ position: [10, 8, 10], fov: 75 }}
        className="w-full h-full"
        gl={{ antialias: true, alpha: true }}
      >
        {/* Cosmic Lighting */}
        <ambientLight intensity={0.1} color="#4a5568" />
        <pointLight position={[20, 20, 20]} intensity={0.5} color="#00ffff" />
        <pointLight
          position={[-20, -20, -20]}
          intensity={0.3}
          color="#ff00ff"
        />
        <pointLight position={[0, 30, 0]} intensity={0.4} color="#ffff00" />

        {/* Cosmic Background Particles */}
        <CosmicParticles />

        {/* Camera Controller */}
        <CosmicCameraController />

        {/* Cosmic Roadmap Nodes */}
        {track.roadmap.map((node) => (
          <CosmicNode
            key={node.id}
            node={node}
            isSelected={selectedNode?.id === node.id}
            onClick={() => setSelectedNode(node)}
            trackColor={track.color}
            glowColor={track.glowColor}
          />
        ))}

        {/* Cosmic Energy Trails */}
        <CosmicEnergyTrails
          roadmap={track.roadmap}
          trackColor={track.color}
          glowColor={track.glowColor}
        />

        {/* Enhanced Orbit Controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={5}
          maxDistance={50}
          autoRotate={true}
          autoRotateSpeed={0.2}
          dampingFactor={0.05}
          enableDamping={true}
        />
      </Canvas>

      {/* Cosmic Navigation Instructions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
        className="absolute bottom-6 left-6 bg-black/80 backdrop-blur-md p-6 rounded-lg border max-w-sm"
        style={{
          borderColor: track.glowColor,
          boxShadow: `0 0 20px ${track.glowColor}30`,
        }}
      >
        <h3 className="font-bold text-white mb-3 font-mono">
          Navigation Controls:
        </h3>
        <ul className="text-sm text-cyan-300 space-y-2">
          <li className="flex items-center space-x-2">
            <div className="w-2 h-2 rounded-full bg-cyan-400"></div>
            <span>Drag to rotate the cosmic view</span>
          </li>
          <li className="flex items-center space-x-2">
            <div className="w-2 h-2 rounded-full bg-magenta-400"></div>
            <span>Scroll to zoom through space</span>
          </li>
          <li className="flex items-center space-x-2">
            <div className="w-2 h-2 rounded-full bg-green-400"></div>
            <span>Click nodes to activate them</span>
          </li>
          <li className="flex items-center space-x-2">
            <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
            <span>Auto-rotation enabled</span>
          </li>
        </ul>
      </motion.div>
    </motion.div>
  );
};

// Cosmic Track Card Component
const CosmicTrackCard = ({ track, onClick }) => {
  const IconComponent = track.icon;

  return (
    <motion.div
      whileHover={{
        scale: 1.05,
        y: -10,
        boxShadow: `0 20px 40px ${track.glowColor}40`,
      }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className="relative bg-gradient-to-br from-gray-900 to-black rounded-xl overflow-hidden cursor-pointer group border"
      style={{
        borderColor: `${track.glowColor}40`,
        boxShadow: `0 0 20px ${track.glowColor}20`,
      }}
    >
      {/* Cosmic Background Effect */}
      <div
        className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-500"
        style={{
          background: `radial-gradient(circle at 50% 50%, ${track.glowColor}40 0%, transparent 70%)`,
        }}
      />

      {/* Animated Border Glow */}
      <div
        className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
        style={{
          background: `linear-gradient(45deg, ${track.glowColor}20, transparent, ${track.glowColor}20)`,
          backgroundSize: "200% 200%",
          animation: "gradient-shift 3s ease infinite",
        }}
      />

      <div className="relative p-8">
        <div className="flex flex-col items-center text-center space-y-6">
          {/* Cosmic Icon */}
          <motion.div
            whileHover={{ rotate: 360, scale: 1.2 }}
            transition={{ duration: 0.8 }}
            className="w-20 h-20 rounded-full flex items-center justify-center relative"
            style={{
              backgroundColor: `${track.glowColor}20`,
              boxShadow: `0 0 30px ${track.glowColor}60`,
            }}
          >
            <IconComponent
              className="w-10 h-10"
              style={{ color: track.glowColor }}
            />
            {/* Pulsing Ring */}
            <div
              className="absolute inset-0 rounded-full animate-pulse"
              style={{
                border: `2px solid ${track.glowColor}60`,
              }}
            />
          </motion.div>

          {/* Track Name */}
          <h3 className="text-xl font-bold text-white group-hover:text-cyan-300 transition-colors duration-300 font-mono">
            {track.name}
          </h3>

          {/* Energy Bar */}
          <div className="w-full h-2 bg-gray-800 rounded-full overflow-hidden relative">
            <motion.div
              initial={{ width: 0 }}
              whileInView={{ width: "100%" }}
              transition={{ duration: 2, delay: 0.3 }}
              className="h-full rounded-full relative"
              style={{ backgroundColor: track.glowColor }}
            >
              <div
                className="absolute inset-0 rounded-full animate-pulse"
                style={{
                  background: `linear-gradient(90deg, transparent, ${track.glowColor}80, transparent)`,
                }}
              />
            </motion.div>
          </div>

          {/* Call to Action */}
          <motion.p
            whileHover={{ scale: 1.05 }}
            className="text-sm text-cyan-300 font-medium"
          >
            Enter Neural Network →
          </motion.p>
        </div>
      </div>
    </motion.div>
  );
};

// Main Cosmic Tracks Component
const Tracks = () => {
  const [selectedTrack, setSelectedTrack] = useState(null);

  const handleTrackSelect = (track) => {
    setSelectedTrack(track);
  };

  const handleBackToTracks = () => {
    setSelectedTrack(null);
  };

  return (
    <div
      className="min-h-screen relative overflow-hidden"
      style={{
        background: `
          radial-gradient(ellipse at top left, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(ellipse at top right, rgba(255, 0, 128, 0.2) 0%, transparent 50%),
          radial-gradient(ellipse at bottom left, rgba(0, 255, 255, 0.2) 0%, transparent 50%),
          radial-gradient(ellipse at bottom right, rgba(255, 215, 0, 0.2) 0%, transparent 50%),
          linear-gradient(180deg, #000000 0%, #0a0a0a 50%, #000000 100%)
        `,
      }}
    >
      {/* Animated Background Stars */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(100)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.2, 1, 0.2],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 2 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <AnimatePresence mode="wait">
        {!selectedTrack ? (
          <motion.div
            key="cosmic-tracks-grid"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="relative z-10 container mx-auto px-6 py-16"
          >
            {/* Cosmic Header */}
            <div className="text-center mb-16">
              <motion.div
                initial={{ opacity: 0, y: -30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, ease: "easeOut" }}
                className="mb-8"
              >
                <h1 className="text-6xl md:text-7xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 mb-6 font-mono">
                  COSMIC CAREER PATHS
                </h1>
                <div className="w-32 h-1 bg-gradient-to-r from-cyan-400 to-purple-400 mx-auto rounded-full"></div>
              </motion.div>

              <motion.p
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.3 }}
                className="text-xl text-cyan-300 max-w-4xl mx-auto leading-relaxed"
              >
                Navigate through the galaxy of technology careers. Each
                constellation represents a unique learning pathway with
                interconnected skills and knowledge nodes waiting to be
                explored.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="mt-8 text-yellow-400 text-lg font-mono"
              >
                ✨ Choose your destiny among the stars ✨
              </motion.div>
            </div>

            {/* Cosmic Tracks Grid */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 0.8 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto"
            >
              {careerTracks.map((track, index) => (
                <motion.div
                  key={track.id}
                  initial={{ opacity: 0, y: 100, rotateX: -30 }}
                  animate={{ opacity: 1, y: 0, rotateX: 0 }}
                  transition={{
                    duration: 0.8,
                    delay: index * 0.15,
                    ease: "easeOut",
                  }}
                  style={{ perspective: "1000px" }}
                >
                  <CosmicTrackCard
                    track={track}
                    onClick={() => handleTrackSelect(track)}
                  />
                </motion.div>
              ))}
            </motion.div>

            {/* Cosmic Footer */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 2 }}
              className="text-center mt-20"
            >
              <div className="inline-block bg-black/60 backdrop-blur-md px-8 py-4 rounded-full border border-cyan-500/30">
                <p className="text-cyan-300 font-mono">
                  Click any constellation to enter its neural network
                </p>
              </div>
            </motion.div>
          </motion.div>
        ) : (
          <CosmicRoadmapScene
            key="cosmic-roadmap-scene"
            track={selectedTrack}
            onBack={handleBackToTracks}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Tracks;
