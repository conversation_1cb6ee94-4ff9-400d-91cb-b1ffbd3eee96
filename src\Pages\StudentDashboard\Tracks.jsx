import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Can<PERSON>, useFrame } from "@react-three/fiber";
import { OrbitControls, Text, Sphere, Line } from "@react-three/drei";
import {
  FiCode,        // Web Development
  FiSmartphone,  // Mobile App Development
  FiDatabase,    // Data Science
  FiShield,      // Cybersecurity
  FiCloud,       // Cloud Computing
  FiSettings,    // DevOps
  FiCpu,         // Embedded & IoT
  FiLayers,      // Blockchain
  FiCheckSquare, // QA & Testing
  FiArrowLeft,   // Back button
  FiPenTool,     // UI/UX Design
} from "react-icons/fi";

import { GiBrain } from "react-icons/gi";          // Machine Learning & AI
import { GiGamepad } from "react-icons/gi"; 

// Career tracks data with roadmap nodes
const careerTracks = export const tracks = [
  {
    id: 1,
    title: "Web Development",
    icon: <FiCode />,
    roadmap: [
      "HTML Basics",
      "CSS Styling",
      "JavaScript Fundamentals",
      "Responsive Design",
      "Git & GitHub",
      "Frontend Frameworks (React/Vue/Angular)",
      "State Management",
      "APIs & REST",
      "GraphQL",
      "Backend Basics (Node.js/Express)",
      "Databases (SQL/NoSQL)",
      "Authentication & Security",
      "Testing (Unit/E2E)",
      "Deployment (CI/CD, Docker, Vercel, Netlify)",
      "Full Stack Projects & Portfolio",
    ],
  },
  {
    id: 2,
    title: "Mobile App Development",
    icon: <FiSmartphone />,
    roadmap: [
      "Java/Kotlin Basics (Android)",
      "Swift/Objective-C (iOS)",
      "Cross-platform (React Native, Flutter)",
      "UI Components & Styling",
      "State Management",
      "APIs Integration",
      "Authentication",
      "Local Storage & Databases",
      "Push Notifications",
      "App Security",
      "App Deployment (Google Play / App Store)",
      "CI/CD for Mobile",
      "Performance Optimization",
      "Testing (Unit/UI/Integration)",
      "Advanced Features (AR/VR, ML Integration)",
    ],
  },
  {
    id: 3,
    title: "Data Science",
    icon: <FiDatabase />,
    roadmap: [
      "Math & Statistics Fundamentals",
      "Python Basics",
      "Data Manipulation (Pandas, Numpy)",
      "Data Visualization (Matplotlib, Seaborn)",
      "Exploratory Data Analysis",
      "SQL & Databases",
      "Machine Learning Basics",
      "Supervised Learning",
      "Unsupervised Learning",
      "Deep Learning Basics",
      "NLP Basics",
      "Big Data Tools (Hadoop, Spark)",
      "Model Deployment",
      "MLOps Basics",
      "Capstone Projects",
    ],
  },
  {
    id: 4,
    title: "Cybersecurity",
    icon: <FiShield />,
    roadmap: [
      "Networking Fundamentals",
      "Operating Systems Security",
      "Linux Basics",
      "Cryptography",
      "Web Security",
      "Vulnerability Assessment",
      "Ethical Hacking",
      "Penetration Testing",
      "Security Tools (Wireshark, Metasploit)",
      "Malware Analysis",
      "Cloud Security",
      "Incident Response",
      "Digital Forensics",
      "Security Compliance & Laws",
      "Advanced Red/Blue Teaming",
    ],
  },
  {
    id: 5,
    title: "Cloud Computing",
    icon: <FiCloud />,
    roadmap: [
      "Cloud Fundamentals",
      "AWS Basics",
      "Azure Basics",
      "GCP Basics",
      "Virtualization",
      "Containers (Docker)",
      "Kubernetes",
      "Serverless Computing",
      "Networking in Cloud",
      "Cloud Databases",
      "Cloud Security",
      "CI/CD in Cloud",
      "Terraform & IaC",
      "Monitoring & Logging",
      "Advanced Cloud Architectures",
    ],
  },
  {
    id: 6,
    title: "DevOps",
    icon: <FiSettings />,
    roadmap: [
      "Linux Basics",
      "Shell Scripting",
      "Git & Version Control",
      "CI/CD Pipelines",
      "Docker",
      "Kubernetes",
      "Infrastructure as Code (Terraform)",
      "Monitoring Tools (Prometheus, Grafana)",
      "Logging & Tracing",
      "Cloud Providers",
      "Security in DevOps",
      "Automation Tools (Ansible, Puppet, Chef)",
      "Performance Testing",
      "DevSecOps",
      "Scaling & High Availability",
    ],
  },
  {
    id: 7,
    title: "Embedded Systems & IoT",
    icon: <FiCpu />,
    roadmap: [
      "C/C++ Basics",
      "Microcontrollers (Arduino, STM32)",
      "Embedded C Programming",
      "RTOS Basics",
      "IoT Fundamentals",
      "IoT Communication Protocols (MQTT, CoAP)",
      "Sensors & Actuators",
      "Wireless Communication (BLE, LoRa, Zigbee)",
      "Cloud IoT Platforms",
      "Edge Computing",
      "Security in IoT",
      "Firmware Development",
      "Testing Embedded Systems",
      "IoT Applications",
      "Smart Devices & Automation",
    ],
  },
  {
    id: 8,
    title: "Blockchain",
    icon: <FiLayers />,
    roadmap: [
      "Blockchain Fundamentals",
      "Cryptography Basics",
      "Ethereum Basics",
      "Smart Contracts",
      "Solidity Programming",
      "DApps Development",
      "Consensus Algorithms",
      "DeFi Basics",
      "NFTs",
      "Layer2 Solutions",
      "Blockchain Security",
      "Interoperability",
      "Scalability Solutions",
      "Testing Smart Contracts",
      "Blockchain Projects",
    ],
  },
  {
    id: 9,
    title: "QA & Testing",
    icon: <FiCheckSquare />,
    roadmap: [
      "Testing Fundamentals",
      "Manual Testing",
      "Agile & Scrum Basics",
      "Unit Testing",
      "Integration Testing",
      "E2E Testing",
      "Automation Testing (Selenium, Cypress)",
      "Performance Testing",
      "Security Testing",
      "Test Management Tools (Jira, TestRail)",
      "API Testing (Postman)",
      "Mobile App Testing",
      "CI/CD Testing",
      "Bug Reporting & Tracking",
      "QA Leadership & Strategy",
    ],
  },
  {
    id: 10,
    title: "UI/UX Design",
    icon: <FiPenTool />,
    roadmap: [
      "Design Fundamentals",
      "Color Theory & Typography",
      "Wireframing",
      "Prototyping (Figma, Adobe XD)",
      "User Research",
      "User Personas",
      "Accessibility Standards",
      "Design Systems",
      "Motion Design",
      "UI Patterns",
      "Responsive Design",
      "Usability Testing",
      "Collaboration with Developers",
      "DesignOps",
      "Portfolio & Case Studies",
    ],
  },
  {
    id: 11,
    title: "Machine Learning & AI",
    icon: <GiBrain />,
    roadmap: [
      "Math & Linear Algebra",
      "Python for ML",
      "Data Preprocessing",
      "Supervised ML",
      "Unsupervised ML",
      "Neural Networks",
      "Deep Learning",
      "CNNs",
      "RNNs",
      "Transformers",
      "Reinforcement Learning",
      "NLP Advanced",
      "MLOps & Deployment",
      "AI Ethics",
      "Research & Advanced Projects",
    ],
  },
  {
    id: 12,
    title: "Game Development",
    icon: <GiGamepad />,
    roadmap: [
      "Game Design Principles",
      "Programming Basics (C#/C++)",
      "Game Engines (Unity/Unreal)",
      "2D Game Development",
      "3D Game Development",
      "Physics in Games",
      "AI in Games",
      "Multiplayer Systems",
      "Animation & Rigging",
      "Audio in Games",
      "Shaders & Graphics",
      "Optimization Techniques",
      "Testing Games",
      "Publishing Games",
      "Monetization & Marketing",
    ],
  },
];

// 3D Node Component
const RoadmapNode = ({ node, isSelected, onClick, trackColor }) => {
  const meshRef = useRef();
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
      if (isSelected || hovered) {
        meshRef.current.scale.setScalar(
          1.2 + Math.sin(state.clock.elapsedTime * 2) * 0.1
        );
      } else {
        meshRef.current.scale.setScalar(1);
      }
    }
  });

  const nodeColor = isSelected ? "#FFD700" : hovered ? "#FFA500" : trackColor;

  return (
    <group position={node.position}>
      <Sphere
        ref={meshRef}
        args={[0.3, 16, 16]}
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <meshStandardMaterial
          color={nodeColor}
          emissive={isSelected ? "#FFD700" : "#000000"}
          emissiveIntensity={isSelected ? 0.3 : 0}
        />
      </Sphere>
      <Text
        position={[0, -0.6, 0]}
        fontSize={0.2}
        color="#274C77"
        anchorX="center"
        anchorY="middle"
        maxWidth={2}
      >
        {node.title}
      </Text>
    </group>
  );
};

// Connection Lines Component
const ConnectionLines = ({ roadmap, trackColor }) => {
  const lines = [];

  // Create connections between sequential nodes
  for (let i = 0; i < roadmap.length - 1; i++) {
    const start = roadmap[i].position;
    const end = roadmap[i + 1].position;

    lines.push(
      <Line
        key={`line-${i}`}
        points={[start, end]}
        color={trackColor}
        lineWidth={2}
        opacity={0.6}
      />
    );
  }

  return <>{lines}</>;
};

// 3D Roadmap Scene
const RoadmapScene = ({ track, onBack }) => {
  const [selectedNode, setSelectedNode] = useState(null);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.5 }}
      className="fixed inset-0 bg-gradient-to-br from-slate-50 to-blue-50 z-50"
    >
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 p-6 bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <FiArrowLeft className="w-5 h-5" />
              <span>Back to Tracks</span>
            </button>
            <div className="flex items-center space-x-3">
              <track.icon className="w-8 h-8" style={{ color: track.color }} />
              <h1 className="text-2xl font-bold text-gray-800">
                {track.name} Roadmap
              </h1>
            </div>
          </div>
          {selectedNode && (
            <div className="bg-white px-4 py-2 rounded-lg shadow-lg">
              <p className="text-sm text-gray-600">Selected:</p>
              <p className="font-semibold text-gray-800">
                {selectedNode.title}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [5, 5, 5], fov: 60 }}
        className="w-full h-full"
      >
        <ambientLight intensity={0.6} />
        <pointLight position={[10, 10, 10]} intensity={1} />
        <pointLight position={[-10, -10, -10]} intensity={0.5} />

        {/* Roadmap Nodes */}
        {track.roadmap.map((node) => (
          <RoadmapNode
            key={node.id}
            node={node}
            isSelected={selectedNode?.id === node.id}
            onClick={() => setSelectedNode(node)}
            trackColor={track.color}
          />
        ))}

        {/* Connection Lines */}
        <ConnectionLines roadmap={track.roadmap} trackColor={track.color} />

        {/* Orbit Controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={3}
          maxDistance={15}
        />
      </Canvas>

      {/* Instructions */}
      <div className="absolute bottom-6 left-6 bg-white/90 backdrop-blur-sm p-4 rounded-lg shadow-lg max-w-sm">
        <h3 className="font-semibold text-gray-800 mb-2">How to Navigate:</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Click and drag to rotate the view</li>
          <li>• Scroll to zoom in/out</li>
          <li>• Click on nodes to select them</li>
          <li>• Right-click and drag to pan</li>
        </ul>
      </div>
    </motion.div>
  );
};

// Track Card Component
const TrackCard = ({ track, onClick }) => {
  const IconComponent = track.icon;

  return (
    <motion.div
      whileHover={{ scale: 1.05, y: -5 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden group"
    >
      <div className="p-6">
        <div className="flex flex-col items-center text-center space-y-4">
          <div
            className="w-16 h-16 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
            style={{ backgroundColor: `${track.color}20` }}
          >
            <IconComponent className="w-8 h-8" style={{ color: track.color }} />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors">
            {track.name}
          </h3>
          <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
            <motion.div
              initial={{ width: 0 }}
              whileInView={{ width: "100%" }}
              transition={{ duration: 1, delay: 0.2 }}
              className="h-full rounded-full"
              style={{ backgroundColor: track.color }}
            />
          </div>
          <p className="text-sm text-gray-600">Explore the roadmap →</p>
        </div>
      </div>
    </motion.div>
  );
};

// Main Tracks Component
const Tracks = () => {
  const [selectedTrack, setSelectedTrack] = useState(null);

  const handleTrackSelect = (track) => {
    setSelectedTrack(track);
  };

  const handleBackToTracks = () => {
    setSelectedTrack(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <AnimatePresence mode="wait">
        {!selectedTrack ? (
          <motion.div
            key="tracks-grid"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto px-6 py-12"
          >
            {/* Header */}
            <div className="text-center mb-12">
              <motion.h1
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-4xl md:text-5xl font-bold text-gray-800 mb-4"
              >
                Choose your career track and explore your roadmap 🚀
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-xl text-gray-600 max-w-3xl mx-auto"
              >
                Discover the most popular career paths in technology and see the
                skills you need to master
              </motion.p>
            </div>

            {/* Tracks Grid */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            >
              {careerTracks.map((track, index) => (
                <motion.div
                  key={track.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <TrackCard
                    track={track}
                    onClick={() => handleTrackSelect(track)}
                  />
                </motion.div>
              ))}
            </motion.div>

            {/* Footer */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 1 }}
              className="text-center mt-16"
            >
              <p className="text-gray-500">
                Click on any track to explore its interactive 3D roadmap
              </p>
            </motion.div>
          </motion.div>
        ) : (
          <RoadmapScene
            key="roadmap-scene"
            track={selectedTrack}
            onBack={handleBackToTracks}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Tracks;
